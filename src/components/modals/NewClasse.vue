<script setup lang="ts">
import { But<PERSON> } from '@/components/ui/button'
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import {
    Select,
    SelectContent,
    SelectGroup,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from '@/components/ui/select'
</script>

<template>
    <Dialog>
        <DialogTrigger as-child>
            <Button size="md" class="rounded-md">
                <span class="iconify hugeicons--plus-sign"></span>
                <span class="hidden sm:flex">
                    Nouvelle classe
                </span>
            </Button>
        </DialogTrigger>
        <DialogContent class="sm:max-w-[540px]">
            <DialogHeader>
                <DialogTitle>Nouvelle classe</DialogTitle>
                <DialogDescription>
                    Enregistrer une nouvelle classe
                </DialogDescription>
            </DialogHeader>
            <div class="grid gap-4 py-4">
                <div class="flex flex-col space-y-1.5 flex-1">
                    <Label for="niveau" class="text-sm font-medium">
                        Niveau
                    </Label>
                    <Select id="niveau">
                        <SelectTrigger id="niveau" class="h-10 w-full">
                            <SelectValue placeholder="Select a verified email to display" />
                        </SelectTrigger>
                        <SelectContent>
                            <SelectGroup>
                                <SelectItem value="<EMAIL>">
                                    <EMAIL>
                                </SelectItem>
                                <SelectItem value="<EMAIL>">
                                    <EMAIL>
                                </SelectItem>
                                <SelectItem value="<EMAIL>">
                                    <EMAIL>
                                </SelectItem>
                            </SelectGroup>
                        </SelectContent>
                    </Select>
                </div>
                <div class="flex flex-col space-y-1.5">
                    <Label for="filliere_name" class="text-sm font-medium">
                        Désignation
                    </Label>
                    <Input type="text" id="filliere_name" name="filliere_name" placeholder="Lettres"
                        class="w-full h-10 border border-gray-200/40 bg-white transition-all" />
                </div>
                <div class="flex flex-col space-y-1.5 flex-1">
                    <Label for="niveau" class="text-sm font-medium">
                        Filière
                    </Label>
                    <Select id="niveau">
                        <SelectTrigger id="niveau" class="h-10 w-full">
                            <SelectValue placeholder="Select a verified email to display" />
                        </SelectTrigger>
                        <SelectContent>
                            <SelectGroup>
                                <SelectItem value="<EMAIL>">
                                    <EMAIL>
                                </SelectItem>
                                <SelectItem value="<EMAIL>">
                                    <EMAIL>
                                </SelectItem>
                                <SelectItem value="<EMAIL>">
                                    <EMAIL>
                                </SelectItem>
                            </SelectGroup>
                        </SelectContent>
                    </Select>
                </div>
                <div class="flex flex-col space-y-1.5 flex-1">
                    <Label for="niveau" class="text-sm font-medium">
                        Cycle
                    </Label>
                    <Select id="niveau">
                        <SelectTrigger id="niveau" class="h-10 w-full">
                            <SelectValue placeholder="Select a verified email to display" />
                        </SelectTrigger>
                        <SelectContent>
                            <SelectGroup>
                                <SelectItem value="<EMAIL>">
                                    <EMAIL>
                                </SelectItem>
                                <SelectItem value="<EMAIL>">
                                    <EMAIL>
                                </SelectItem>
                                <SelectItem value="<EMAIL>">
                                    <EMAIL>
                                </SelectItem>
                            </SelectGroup>
                        </SelectContent>
                    </Select>
                </div>
                <div class="mt-1 pb-2">
                    <p class="text-sm text-foreground-muted">
                        * Tous les champs sont obligatoires
                    </p>
                </div>
            </div>
            <DialogFooter class="flex justify-end gap-2 items-center">
                <Button size="sm" class="h-9" variant="outline">
                    <span class="iconify flex hugeicons--cancel-01 mr-1"></span>
                    Annuler
                </Button>
                <Button size="sm" class="h-9" type="submit">
                    <span class="iconify flex hugeicons--floppy-disk mr-1"></span>
                    Enregistrer
                </Button>
            </DialogFooter>
        </DialogContent>
    </Dialog>
</template>