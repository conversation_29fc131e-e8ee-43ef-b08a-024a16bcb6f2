<script setup lang="ts">
import FormSection from '@/components/atoms/FormSection.vue';
import InputWrapper from '@/components/atoms/InputWrapper.vue';
import SpanRequired from '@/components/atoms/SpanRequired.vue';
import DashFormLayout from '@/components/templates/DashFormLayout.vue';
import { Button } from '@/components/ui/button';
import CustomDatePicker from '@/components/ui/CustomDatePicker.vue';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectGroup, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';


</script>
<template>
    <DashFormLayout title="Ajouter un nouveau apprenant" link-back="/apprenants/operations"
        current-mode="formel" group="operations" active-tag-name="inscription">
        <form class="w-full flex flex-col space-y-8">
            <FormSection class="grid sm:grid-cols-2 lg:grid-cols-3 gap-y-6 gap-x-10" title="Informations personnelles">
                <div class="col-span-full *:h-max grid sm:grid-cols-2 lg:grid-cols-3 gap-y-6 gap-x-10">
                    <div class="sm:row-span-2 flex flex-col space-y-1.5">
                        <Label class="text-sm">
                            Image
                        </Label>
                        <div class="flex flex-1 h-auto">
                            <div class="w-full py-3  bg-gray-50 rounded-md border border-gray-300 border-dashed">
                                <div class="grid gap-3">
                                    <div class="flex flex-col items-center text-center">
                                        <span
                                            class="mx-auto mb-1 text-2xl text-primary iconify hugeicons--image-01"></span>
                                        <h2 class="text-center text-gray-400   text-xs font-light leading-4">PNG,
                                            JPG or PDF, smaller than 15MB</h2>
                                    </div>
                                    <div class="grid gap-2">
                                        <div class="flex items-center justify-center">
                                            <label>
                                                <input type="file" hidden />
                                                <div
                                                    class="flex w-max h-9 px-3 flex-col bg-primary rounded-md shadow text-white text-xs font-semibold leading-4 items-center justify-center cursor-pointer focus:outline-none">
                                                    Choisir une image</div>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <InputWrapper>
                        <Label class="text-sm">
                            Matricule
                            <SpanRequired />
                        </Label>
                        <Input class="bg-white transition-all h-10 rounded-md" placeholder="Isaac" />
                    </InputWrapper>
                    <InputWrapper>
                        <Label class="text-sm">
                            Nom
                            <SpanRequired />
                        </Label>
                        <Input class="bg-white transition-all h-10 rounded-md" placeholder="Kasongo" />
                    </InputWrapper>
                    <InputWrapper>
                        <Label class="text-sm">
                            Post nom
                            <SpanRequired />
                        </Label>
                        <Input class="bg-white transition-all h-10 rounded-md" placeholder="Muleka" />
                    </InputWrapper>
                    <InputWrapper>
                        <Label class="text-sm">
                            Prenom
                            <SpanRequired />
                        </Label>
                        <Input class="bg-white transition-all h-10 rounded-md" placeholder="Isaac" />
                    </InputWrapper>
                </div>
                <InputWrapper>
                    <Label class="text-sm">
                        Date d’inscription
                        <SpanRequired />
                    </Label>
                    <CustomDatePicker />
                </InputWrapper>
                <InputWrapper>
                    <Label class="text-sm">
                        Genre
                        <SpanRequired />
                    </Label>
                    <Select>
                        <SelectTrigger id="genre" class="!h-10 bg-white w-full">
                            <SelectValue placeholder="Sélectionner le genre" />
                        </SelectTrigger>
                        <SelectContent>
                            <SelectGroup>
                                <SelectItem value="masculin">Masculin</SelectItem>
                                <SelectItem value="feminin">Feminin</SelectItem>
                            </SelectGroup>
                        </SelectContent>
                    </Select>
                </InputWrapper>
                <InputWrapper>
                    <Label class="text-sm">
                        Etat civil
                        <SpanRequired />
                    </Label>
                    <Select>
                        <SelectTrigger id="etat-civil" class="!h-10 bg-white w-full">
                            <SelectValue placeholder="Sélectionner l'état civil" />
                        </SelectTrigger>
                        <SelectContent>
                            <SelectGroup>
                                <SelectItem value="celibataire">Célibataire</SelectItem>
                                <SelectItem value="marie">Marié(e)</SelectItem>
                                <SelectItem value="divorce">Divorcé(e)</SelectItem>
                                <SelectItem value="veuf">Veuf(ve)</SelectItem>
                            </SelectGroup>
                        </SelectContent>
                    </Select>
                </InputWrapper>
                <InputWrapper>
                    <Label class="text-sm">
                        Nationnalité actuelle
                        <SpanRequired />
                    </Label>
                    <Select>
                        <SelectTrigger id="etat-civil" class="!h-10 bg-white w-full">
                            <SelectValue placeholder="Sélectionner l'état civil" />
                        </SelectTrigger>
                        <SelectContent>
                            <SelectGroup>
                                <SelectItem value="celibataire">Célibataire</SelectItem>
                                <SelectItem value="marie">Marié(e)</SelectItem>
                                <SelectItem value="divorce">Divorcé(e)</SelectItem>
                                <SelectItem value="veuf">Veuf(ve)</SelectItem>
                            </SelectGroup>
                        </SelectContent>
                    </Select>
                </InputWrapper>
                <InputWrapper>
                    <Label class="text-sm">
                        Date de naissance
                        <SpanRequired />
                    </Label>
                    <CustomDatePicker />
                </InputWrapper>
                <InputWrapper>
                    <Label class="text-sm">
                        Age
                        <SpanRequired />
                    </Label>
                    <Input class="bg-white transition-all h-10 rounded-md" readonly />
                </InputWrapper>
                <InputWrapper>
                    <Label class="text-sm">
                        Lieu de naissance
                    </Label>
                    <Input class="bg-white transition-all h-10 rounded-md" />
                </InputWrapper>
                <InputWrapper>
                    <Label class="text-sm">
                        Père
                    </Label>
                    <Input class="bg-white transition-all h-10 rounded-md" />
                </InputWrapper>
                <InputWrapper>
                    <Label class="text-sm">
                        Mère
                    </Label>
                    <Input class="bg-white transition-all h-10 rounded-md" />
                </InputWrapper>

                <InputWrapper>
                    <Label class="text-sm">
                       No carte identité
                    </Label>
                    <Input class="bg-white transition-all h-10 rounded-md" />
                </InputWrapper>
                <InputWrapper>
                    <Label class="text-sm">
                        Téléphone
                    </Label>
                    <Input class="bg-white transition-all h-10 rounded-md" />
                </InputWrapper>
                <InputWrapper>
                    <Label class="text-sm">
                        Adresse email
                    </Label>
                    <Input class="bg-white transition-all h-10 rounded-md" />
                </InputWrapper>

                <InputWrapper>
                    <Label class="text-sm">
                       Province <SpanRequired/>
                    </Label>
                    <Input class="bg-white transition-all h-10 rounded-md" />
                </InputWrapper>
                <InputWrapper>
                    <Label class="text-sm">
                        Ville/ territoire <SpanRequired/>
                    </Label>
                    <Input class="bg-white transition-all h-10 rounded-md" />
                </InputWrapper>
                <InputWrapper>
                    <Label class="text-sm">
                        Commune
                    </Label>
                    <Input class="bg-white transition-all h-10 rounded-md" />
                </InputWrapper>
                <InputWrapper>
                    <Label class="text-sm">
                        Adresse <SpanRequired/>
                    </Label>
                    <Input class="bg-white transition-all h-10 rounded-md" />
                </InputWrapper>
            </FormSection>
            <div class="w-full flex h-px bg-gray-300"></div>
            <FormSection class="grid sm:grid-cols-2 lg:grid-cols-3 gap-y-6 gap-x-10" title="Formation">
                <InputWrapper>
                    <Label class="text-sm">
                        Dernier niveau reussi
                        <SpanRequired />
                    </Label>
                    <Select>
                        <SelectTrigger id="etat-civil" class="!h-10 bg-white w-full">
                            <SelectValue placeholder="Sélectionner l'état civil" />
                        </SelectTrigger>
                        <SelectContent>
                            <SelectGroup>
                                <SelectItem value="celibataire">Célibataire</SelectItem>
                                <SelectItem value="marie">Marié(e)</SelectItem>
                                <SelectItem value="divorce">Divorcé(e)</SelectItem>
                                <SelectItem value="veuf">Veuf(ve)</SelectItem>
                            </SelectGroup>
                        </SelectContent>
                    </Select>
                </InputWrapper>
                <InputWrapper>
                    <Label class="text-sm">
                        Année scolaire
                        <SpanRequired />
                    </Label>
                    <Select>
                        <SelectTrigger id="etat-civil" class="!h-10 bg-white w-full">
                            <SelectValue placeholder="Sélectionner l'état civil" />
                        </SelectTrigger>
                        <SelectContent>
                            <SelectGroup>
                                <SelectItem value="celibataire">Célibataire</SelectItem>
                                <SelectItem value="marie">Marié(e)</SelectItem>
                                <SelectItem value="divorce">Divorcé(e)</SelectItem>
                                <SelectItem value="veuf">Veuf(ve)</SelectItem>
                            </SelectGroup>
                        </SelectContent>
                    </Select>
                </InputWrapper>
                <InputWrapper>
                    <Label class="text-sm">
                        Filière
                        <SpanRequired />
                    </Label>
                    <Select>
                        <SelectTrigger id="etat-civil" class="!h-10 bg-white w-full">
                            <SelectValue placeholder="Sélectionner l'état civil" />
                        </SelectTrigger>
                        <SelectContent>
                            <SelectGroup>
                                <SelectItem value="celibataire">Célibataire</SelectItem>
                                <SelectItem value="marie">Marié(e)</SelectItem>
                                <SelectItem value="divorce">Divorcé(e)</SelectItem>
                                <SelectItem value="veuf">Veuf(ve)</SelectItem>
                            </SelectGroup>
                        </SelectContent>
                    </Select>
                </InputWrapper>
                <InputWrapper>
                    <Label class="text-sm">
                        Niveau
                        <SpanRequired />
                    </Label>
                    <Select>
                        <SelectTrigger id="etat-civil" class="!h-10 bg-white w-full">
                            <SelectValue placeholder="Sélectionner l'état civil" />
                        </SelectTrigger>
                        <SelectContent>
                            <SelectGroup>
                                <SelectItem value="celibataire">Célibataire</SelectItem>
                                <SelectItem value="marie">Marié(e)</SelectItem>
                                <SelectItem value="divorce">Divorcé(e)</SelectItem>
                                <SelectItem value="veuf">Veuf(ve)</SelectItem>
                            </SelectGroup>
                        </SelectContent>
                    </Select>
                </InputWrapper>
                <InputWrapper>
                    <Label class="text-sm">
                        Classe
                        <SpanRequired />
                    </Label>
                    <Select>
                        <SelectTrigger id="etat-civil" class="!h-10 bg-white w-full">
                            <SelectValue placeholder="Sélectionner l'état civil" />
                        </SelectTrigger>
                        <SelectContent>
                            <SelectGroup>
                                <SelectItem value="celibataire">Célibataire</SelectItem>
                                <SelectItem value="marie">Marié(e)</SelectItem>
                                <SelectItem value="divorce">Divorcé(e)</SelectItem>
                                <SelectItem value="veuf">Veuf(ve)</SelectItem>
                            </SelectGroup>
                        </SelectContent>
                    </Select>
                </InputWrapper>
                <InputWrapper>
                    <Label class="text-sm">
                        Type d’inscription
                        <SpanRequired />
                    </Label>
                    <Select>
                        <SelectTrigger id="etat-civil" class="!h-10 bg-white w-full">
                            <SelectValue placeholder="Sélectionner l'état civil" />
                        </SelectTrigger>
                        <SelectContent>
                            <SelectGroup>
                                <SelectItem value="celibataire">Célibataire</SelectItem>
                                <SelectItem value="marie">Marié(e)</SelectItem>
                                <SelectItem value="divorce">Divorcé(e)</SelectItem>
                                <SelectItem value="veuf">Veuf(ve)</SelectItem>
                            </SelectGroup>
                        </SelectContent>
                    </Select>
                </InputWrapper>

                <InputWrapper>
                    <Label class="text-sm">
                        Cycle
                        <SpanRequired />
                    </Label>
                    <Select>
                        <SelectTrigger id="etat-civil" class="!h-10 bg-white w-full">
                            <SelectValue placeholder="Sélectionner l'état civil" />
                        </SelectTrigger>
                        <SelectContent>
                            <SelectGroup>
                                <SelectItem value="celibataire">Célibataire</SelectItem>
                                <SelectItem value="marie">Marié(e)</SelectItem>
                                <SelectItem value="divorce">Divorcé(e)</SelectItem>
                                <SelectItem value="veuf">Veuf(ve)</SelectItem>
                            </SelectGroup>
                        </SelectContent>
                    </Select>
                </InputWrapper>
                <InputWrapper>
                    <Label class="text-sm">
                        Année d’obtention
                        <SpanRequired />
                    </Label>
                    <Select>
                        <SelectTrigger id="etat-civil" class="!h-10 bg-white w-full">
                            <SelectValue placeholder="Sélectionner l'état civil" />
                        </SelectTrigger>
                        <SelectContent>
                            <SelectGroup>
                                <SelectItem value="celibataire">Célibataire</SelectItem>
                                <SelectItem value="marie">Marié(e)</SelectItem>
                                <SelectItem value="divorce">Divorcé(e)</SelectItem>
                                <SelectItem value="veuf">Veuf(ve)</SelectItem>
                            </SelectGroup>
                        </SelectContent>
                    </Select>
                </InputWrapper>
                <InputWrapper>
                    <Label class="text-sm">
                        Ecole de provenance
                        <SpanRequired />
                    </Label>
                    <Select>
                        <SelectTrigger id="etat-civil" class="!h-10 bg-white w-full">
                            <SelectValue placeholder="Sélectionner l'état civil" />
                        </SelectTrigger>
                        <SelectContent>
                            <SelectGroup>
                                <SelectItem value="celibataire">Célibataire</SelectItem>
                                <SelectItem value="marie">Marié(e)</SelectItem>
                                <SelectItem value="divorce">Divorcé(e)</SelectItem>
                                <SelectItem value="veuf">Veuf(ve)</SelectItem>
                            </SelectGroup>
                        </SelectContent>
                    </Select>
                </InputWrapper>
            </FormSection>
            <div class="flex items-center justify-end gap-2">
                <Button variant="outline">
                    <span class="flex iconify hugeicons--cancel-01 mr-1.5"></span>
                    Annuler
                </Button>
                <Button>
                    <span class="flex iconify hugeicons--floppy-disk mr-1.5"></span>
                    Enregistrer
                </Button>
            </div>
        </form>
    </DashFormLayout>
</template>