<script setup lang="ts">
import LayoutGestionDisciplinaire from '@/components/templates/LayoutGestionDisciplinaire.vue';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';


import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from '@/components/ui/table'
import { Checkbox } from '@/components/ui/checkbox';

import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'

// Structure pour les sorties d'élèves
const sortiesEleves = [
    {
        id: 1,
        date: "2024-12-15",
        nom: "Mwamba",
        prenom: "Jean",
        heureSortie: "14:30",
        motif: "Rendez-vous médical",
        filiere: "Lettres",
        anneeScolaire: "2024-2025",
        semestre: "1er Semestre"
    },
    {
        id: 2,
        date: "2024-12-15",
        nom: "Kason<PERSON>",
        prenom: "<PERSON>",
        heureSortie: "15:45",
        motif: "Urgence familiale",
        filiere: "Sciences",
        anneeScolaire: "2024-2025",
        semestre: "1er Semestre"
    },
    {
        id: 3,
        date: "2024-12-16",
        nom: "Ilunga",
        prenom: "Patrick",
        heureSortie: "13:15",
        motif: "Convocation administrative",
        filiere: "Mathématiques",
        anneeScolaire: "2024-2025",
        semestre: "1er Semestre"
    },
    {
        id: 4,
        date: "2024-12-16",
        nom: "Tshibanda",
        prenom: "Aline",
        heureSortie: "16:00",
        motif: "Problème de transport",
        filiere: "Lettres",
        anneeScolaire: "2024-2025",
        semestre: "1er Semestre"
    },
]

</script>
<template>
    <LayoutGestionDisciplinaire active-tag-name="index">
        <div class="flex flex-col md:flex-row gap-3 md:items-center md:justify-between">
            <div class="relative max-w-sm w-full">
                <Input type="text" id="search" name="search" placeholder="Rechercher une filiere..."
                    class="w-full ps-10 border border-gray-200/40 bg-white transition-all h-9 rounded-md" />
                <div class="absolute left-3 top-1/2 transform -translate-y-1/2 text-foreground-muted/70">
                    <span class="flex iconify hugeicons--search-01 text-sm"></span>
                </div>
            </div>
            <div class="flex flex-wrap items-center gap-2.5">
                <DropdownMenu>
                    <DropdownMenuTrigger as-child>
                        <Button variant="ghost" size="md" class="bg-white border border-border rounded-md ">
                            Exporter
                            <span class="iconify hugeicons--arrow-down-01 " />
                        </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent>
                        <DropdownMenuItem class="flex items-center">
                            <span class="flex mr-1.5 iconify hugeicons--pdf-02"></span>
                            Exporter pdf
                        </DropdownMenuItem>
                        <DropdownMenuItem class="flex items-center">
                            <span class="flex mr-1.5 iconify hugeicons--ai-sheets"></span>
                            Exporter Excel
                        </DropdownMenuItem>
                    </DropdownMenuContent>
                </DropdownMenu>
                <Button size="md" class="">
                    <span class="flex iconify hugeicons--plus-sign"></span>
                    Ajouter
                </Button>
            </div>
        </div>
        <div class="mt-4 rounded-md overflow-hidden">
            <Table class="rounded-md bg-white">
                <TableHeader>
                    <TableRow>
                        <TableHead class="w-[20px]">
                            <Checkbox class="bg-white scale-70" />
                        </TableHead>
                        <TableHead>Date</TableHead>
                        <TableHead>Nom et prénom</TableHead>
                        <TableHead>Heure de sortie</TableHead>
                        <TableHead>Motif</TableHead>
                        <TableHead>Filière</TableHead>
                        <TableHead>Année scolaire</TableHead>
                        <TableHead>Semestre</TableHead>
                        <TableHead>

                        </TableHead>
                    </TableRow>
                </TableHeader>
                <TableBody>
                    <TableRow v-for="item in sortiesEleves" :key="item.id">
                        <TableCell class="w-[40px]">
                            <Checkbox class="bg-white scale-70" />
                        </TableCell>
                        <TableCell>{{ item.date }}</TableCell>
                        <TableCell>{{ item.nom }} {{ item.prenom }}</TableCell>
                        <TableCell>{{ item.heureSortie }}</TableCell>
                        <TableCell>{{ item.motif }}</TableCell>
                        <TableCell>{{ item.filiere }}</TableCell>
                        <TableCell>{{ item.anneeScolaire }}</TableCell>
                        <TableCell>{{ item.semestre }}</TableCell>
                        <TableCell>
                            <div class="flex items-center gap-2 w-max">
                                <Button variant="destructive" size="icon" class="size-8">
                                    <span class="iconify hugeicons--delete-02"></span>
                                </Button>
                            </div>
                        </TableCell>
                    </TableRow>
                </TableBody>
            </Table>
        </div>
    </LayoutGestionDisciplinaire>
</template>