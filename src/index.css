@import "tailwindcss";
@import "tw-animate-css";

@plugin "@iconify/tailwind4" {
  prefixes: hugeicons;
  scale: 1.2;
 }

 :root{
  
  --vis-tooltip-background-color: none !important;
    --vis-tooltip-border-color: none !important;
    --vis-tooltip-text-color: none !important;
    --vis-tooltip-shadow-color: none !important;
    --vis-tooltip-backdrop-filter: none !important;
    --vis-tooltip-padding: none !important;

    --vis-primary-color: 206 95% 40%;
    /* change to any hsl value you want */
    --vis-secondary-color: 45 76% 53%;
    --vis-text-color: 259.733 0.034 37.3%;
 }

@theme {
  --color-primary-50: oklch(0.97 0.018 238.5);
  --color-primary-100: oklch(0.94 0.035 238.5);
  --color-primary-200: oklch(0.89 0.058 238.5);
  --color-primary-300: oklch(0.83 0.085 238.5);
  --color-primary-400: oklch(0.72 0.125 238.5);
  --color-primary-500: oklch(0.63 0.155 238.5);
  --color-primary-600: oklch(0.55 0.185 238.5);
  --color-primary-700: oklch(0.45 0.165 238.5);
  --color-primary-800: oklch(0.38 0.145 238.5);
  --color-primary-900: oklch(0.33 0.125 238.5);
  --color-primary-950: oklch(0.22 0.085 238.5);






  --font-mulish: 'Mulish', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen,
    Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}


@theme inline {
  --color-background: var(--color-gray-50);
  --color-dashboard-bg: oklch(0.97 0.02 270);
  --color-background-soft: var(--color-gray-100);
  --color-foreground: var(--color-gray-700);
  --color-foreground-title: var(--color-gray-900);
  --color-foreground-subtitle: var(--color-gray-800);
  --color-foreground-muted: var(--color-gray-600);

  --color-card: var(--color-white);
  --color-card-foreground: var(--color-gray-700);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--color-primary-500);
  --color-primary-foreground: var(--color-primary-50);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);

  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
  --animate-accordion-down: accordion-down 0.2s ease-out;
  --animate-accordion-up: accordion-up 0.2s ease-out;

  @keyframes accordion-down {
    from {
      height: 0;
    }

    to {
      height: var(--reka-accordion-content-height);
    }
  }

  @keyframes accordion-up {
    from {
      height: var(--reka-accordion-content-height);
    }

    to {
      height: 0;
    }
  }
}

:root {
  --dash-background: oklch(0.93 0.019 274.5);
  --background: oklch(1 0 0);
  --foreground: oklch(0.129 0.042 264.695);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.129 0.042 264.695);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.129 0.042 264.695);
  --primary: oklch(0.208 0.042 265.755);
  --primary-foreground: oklch(0.984 0.003 247.858);
  --secondary: oklch(0.968 0.007 247.896);
  --secondary-foreground: oklch(0.208 0.042 265.755);
  --muted: oklch(0.968 0.007 247.896);
  --muted-foreground: oklch(0.554 0.046 257.417);
  --accent: oklch(0.968 0.007 247.896);
  --accent-foreground: oklch(0.208 0.042 265.755);
  --destructive: oklch(0.577 0.245 27.325);
  --destructive-foreground: oklch(0.577 0.245 27.325);
  --border: oklch(0.929 0.013 255.508);
  --input: oklch(0.929 0.013 255.508);
  --ring: oklch(0.704 0.04 256.788);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --radius: 0.625rem;
  --sidebar: oklch(0.984 0.003 247.858);
  --sidebar-foreground: oklch(0.129 0.042 264.695);
  --sidebar-primary: oklch(0.208 0.042 265.755);
  --sidebar-primary-foreground: oklch(0.984 0.003 247.858);
  --sidebar-accent: oklch(0.968 0.007 247.896);
  --sidebar-accent-foreground: oklch(0.208 0.042 265.755);
  --sidebar-border: oklch(0.929 0.013 255.508);
  --sidebar-ring: oklch(0.704 0.04 256.788);
}



@layer base {
  * {
    @apply border-border outline-ring/50;
  }

  body {
    @apply bg-background text-foreground;
  }
}

@layer components{
  .menubottom{
    @apply max-md:fixed max-md:bottom-0 max-md:inset-x-0 max-md:z-[150]
  }
  .raduis-inner{
    border-radius: calc(var(--card-radius) - var(--card-padding));
  }
}
