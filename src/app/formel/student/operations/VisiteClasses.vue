<script setup lang="ts">
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from '@/components/ui/table'
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { Checkbox } from '@/components/ui/checkbox';
import LayoutSaisieOperation from '@/components/templates/LayoutSaisieOperation.vue';
import BoxPanelWrapper from '@/components/atoms/BoxPanelWrapper.vue';


</script>

<template>
    <LayoutSaisieOperation group="operations" active-tag-name="visite-classe">
        <BoxPanelWrapper>
            <div class="flex items-center gap-3 justify-between">
                <div class="relative flex-1">
                    <Input type="text" id="search" name="search" placeholder="Rechercher un cours..."
                        class="w-full max-w-sm ps-10 border border-gray-200/40 bg-white transition-all h-10 rounded-md" />
                    <div class="absolute left-3 top-1/2 transform -translate-y-1/2 text-foreground-muted/70">
                        <span class="flex iconify hugeicons--search-01 text-sm"></span>
                    </div>
                </div>
                <div class="flex flex-wrap items-center gap-2.5">
                    <DropdownMenu>
                        <DropdownMenuTrigger as-child>
                            <Button variant="ghost" size="md" class="bg-white border border-border rounded-md">
                                Exporter
                                <span class="iconify hugeicons--arrow-down-01 " />
                            </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent>
                            <DropdownMenuItem class="flex items-center">
                                <span class="flex mr-1.5 iconify hugeicons--pdf-02"></span>
                                Exporter pdf
                            </DropdownMenuItem>
                            <DropdownMenuItem class="flex items-center">
                                <span class="flex mr-1.5 iconify hugeicons--ai-sheets"></span>
                                Exporter Excel
                            </DropdownMenuItem>
                        </DropdownMenuContent>
                    </DropdownMenu>
                    <Button size="md" class="rounded-md">
                        <span class="flex iconify hugeicons--plus-sign"></span>
                        <span class="hidden sm:flex">Nouvelle visite</span>
                    </Button>
                </div>
            </div>
            <div class="mt-4 rounded-md overflow-hidden">
                <Table class="rounded-md bg-white">
                    <TableHeader class="">
                        <TableRow class="bg-primary">
                            <TableHead rowspan="2" class="w-[20px]">
                                <Checkbox class="bg-white scale-70" />
                            </TableHead>
                            <TableHead rowspan="2">Date de visite</TableHead>
                            <TableHead rowspan="2">Visite de classe</TableHead>
                            <TableHead class="text-center" colspan="3">Classe visitée</TableHead>
                            <TableHead class="text-center" colspan="4">Appréciation détaillée</TableHead>
                            <TableHead rowspan="2">Professeur</TableHead>
                        </TableRow>
                        <TableRow class="bg-primary border-t-primary text-white text-xs">
                            <TableHead class="!rounded-tl-none">Classe</TableHead>
                            <TableHead>Sujet</TableHead>
                            <TableHead>Heure</TableHead>
                            <TableHead>Doc prof (20pts)</TableHead>
                            <TableHead>Méthode proc&eacute;(12pts)</TableHead>
                            <TableHead>Matière (12pts)</TableHead>
                            <TableHead class="!rounded-tr-none">Marche leçon (25pts)</TableHead>
                        </TableRow>
                    </TableHeader>
                    <TableBody>
                        <TableRow>
                            <TableCell>
                                <Checkbox />
                            </TableCell>
                            <TableCell>2024-06-01</TableCell>
                            <TableCell>Visite 1</TableCell>
                            <TableCell>6ème A</TableCell>
                            <TableCell>Mathématiques</TableCell>
                            <TableCell>08:00</TableCell>
                            <TableCell>18</TableCell>
                            <TableCell>10</TableCell>
                            <TableCell>11</TableCell>
                            <TableCell>22</TableCell>
                            <TableCell>M. Dupont</TableCell>
                        </TableRow>
                        <TableRow>
                            <TableCell>
                                <Checkbox />
                            </TableCell>
                            <TableCell>2024-06-02</TableCell>
                            <TableCell>Visite 2</TableCell>
                            <TableCell>5ème B</TableCell>
                            <TableCell>Français</TableCell>
                            <TableCell>10:00</TableCell>
                            <TableCell>19</TableCell>
                            <TableCell>11</TableCell>
                            <TableCell>10</TableCell>
                            <TableCell>23</TableCell>
                            <TableCell>Mme Martin</TableCell>
                        </TableRow>
                        <TableRow>
                            <TableCell>
                                <Checkbox />
                            </TableCell>
                            <TableCell>2024-06-03</TableCell>
                            <TableCell>Visite 3</TableCell>
                            <TableCell>4ème C</TableCell>
                            <TableCell>Histoire</TableCell>
                            <TableCell>09:30</TableCell>
                            <TableCell>17</TableCell>
                            <TableCell>12</TableCell>
                            <TableCell>12</TableCell>
                            <TableCell>21</TableCell>
                            <TableCell>M. Bernard</TableCell>
                        </TableRow>
                        <TableRow>
                            <TableCell>
                                <Checkbox />
                            </TableCell>
                            <TableCell>2024-06-04</TableCell>
                            <TableCell>Visite 4</TableCell>
                            <TableCell>3ème D</TableCell>
                            <TableCell>Sciences</TableCell>
                            <TableCell>11:00</TableCell>
                            <TableCell>20</TableCell>
                            <TableCell>10</TableCell>
                            <TableCell>11</TableCell>
                            <TableCell>24</TableCell>
                            <TableCell>Mme Leroy</TableCell>
                        </TableRow>
                        <TableRow>
                            <TableCell>
                                <Checkbox />
                            </TableCell>
                            <TableCell>2024-06-05</TableCell>
                            <TableCell>Visite 5</TableCell>
                            <TableCell>2nde E</TableCell>
                            <TableCell>Anglais</TableCell>
                            <TableCell>13:00</TableCell>
                            <TableCell>16</TableCell>
                            <TableCell>9</TableCell>
                            <TableCell>10</TableCell>
                            <TableCell>20</TableCell>
                            <TableCell>M. Smith</TableCell>
                        </TableRow>
                        <TableRow>
                            <TableCell>
                                <Checkbox />
                            </TableCell>
                            <TableCell>2024-06-06</TableCell>
                            <TableCell>Visite 6</TableCell>
                            <TableCell>1ère F</TableCell>
                            <TableCell>Philosophie</TableCell>
                            <TableCell>15:00</TableCell>
                            <TableCell>15</TableCell>
                            <TableCell>8</TableCell>
                            <TableCell>9</TableCell>
                            <TableCell>19</TableCell>
                            <TableCell>Mme Dubois</TableCell>
                        </TableRow>
                    </TableBody>
                </Table>
            </div>
        </BoxPanelWrapper>
    </LayoutSaisieOperation>
</template>
