import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'
import { authRoutes } from './auth'
import { module1Routes } from './module1'
import ModuleSelector from '@/app/ModuleSelector.vue'
import { rhModuleRoutes } from './rh-module'

const routes: RouteRecordRaw[] = [
  {
    path: "/",
    name: "root",
    component: ModuleSelector
  },
  ...authRoutes,
  ...module1Routes,
  ...rhModuleRoutes
]


const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes,
})


export default router
