<script setup lang="ts">
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from '@/components/ui/table'
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { Checkbox } from '@/components/ui/checkbox';
import LayoutSaisieOperation from '@/components/templates/LayoutSaisieOperation.vue';
import BoxPanelWrapper from '@/components/atoms/BoxPanelWrapper.vue';




const formateursResponsables = [
    {
        id: 1,
        annee: "2023-2024",
        formation: "Lettres Modernes",
        formateur: "Dr. <PERSON>"
    },
    {
        id: 2,
        annee: "2023-2024",
        formation: "Sciences Physiques",
        formateur: "<PERSON><PERSON>"
    },
    {
        id: 3,
        annee: "2024-2025",
        formation: "Mathématiques",
        formateur: "<PERSON><PERSON> <PERSON>"
    },
]


</script>

<template>
    <LayoutSaisieOperation current-mode="non-formel" group="saisieNonformel" active-tag-name="formateur-responsable">

        <BoxPanelWrapper>
            <div class="flex items-center gap-3 justify-between">
                <div class="relative flex-1">
                    <Input type="text" id="search" name="search" placeholder="Rechercher un cours..."
                        class="w-full max-w-sm ps-10 border border-gray-200/40 bg-white transition-all h-10 rounded-md" />
                    <div class="absolute left-3 top-1/2 transform -translate-y-1/2 text-foreground-muted/70">
                        <span class="flex iconify hugeicons--search-01 text-sm"></span>
                    </div>
                </div>
                <div class="flex flex-wrap items-center gap-2.5">
                    <Button size="md" as-child>
                        <router-link to="/non-formel/apprenants/formateurs/nouveau">
                            <span class="iconify hugeicons--plus-sign"></span>
                            Nouveau
                        </router-link>
                    </Button>
                    <DropdownMenu>
                        <DropdownMenuTrigger as-child>
                            <Button variant="ghost" size="md" class="bg-white border border-border rounded-md">
                                Exporter
                                <span class="iconify hugeicons--arrow-down-01 " />
                            </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent>
                            <DropdownMenuItem class="flex items-center">
                                <span class="flex mr-1.5 iconify hugeicons--pdf-02"></span>
                                Exporter pdf
                            </DropdownMenuItem>
                            <DropdownMenuItem class="flex items-center">
                                <span class="flex mr-1.5 iconify hugeicons--ai-sheets"></span>
                                Exporter Excel
                            </DropdownMenuItem>
                        </DropdownMenuContent>
                    </DropdownMenu>
                </div>
            </div>
            <div class="mt-4 rounded-md overflow-hidden">
                <Table class="rounded-md bg-white">
                    <TableHeader>
                        <TableRow>
                            <TableHead class="w-[20px]">
                                <Checkbox class="bg-white scale-70" />
                            </TableHead>
                            <TableHead class="text-left">
                                Année
                            </TableHead>
                            <TableHead>
                                Formation
                            </TableHead>
                            <TableHead>
                                Formateur
                            </TableHead>
                        </TableRow>
                    </TableHeader>
                    <TableBody>
                        <TableRow v-for="item in formateursResponsables" :key="item.id">
                            <TableCell class="w-[40px]">
                                <Checkbox class="bg-white scale-70" />
                            </TableCell>
                            <TableCell>
                                {{ item.annee }}
                            </TableCell>
                            <TableCell>
                                {{ item.formation }}
                            </TableCell>
                            <TableCell class="text-left">
                                {{ item.formateur }}
                            </TableCell>
                        </TableRow>
                    </TableBody>
                </Table>
            </div>
        </BoxPanelWrapper>

    </LayoutSaisieOperation>

</template>
