<script setup lang="ts">
import LayoutGestionDisciplinaire from '@/components/templates/LayoutGestionDisciplinaire.vue';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'

import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from '@/components/ui/table'
import { Checkbox } from '@/components/ui/checkbox';

// Structure pour les cas d'indiscipline
const casIndisciplines = [
    {
        id: 1,
        date: "2024-12-15",
        apprenant: "Mwa<PERSON> Jean",
        fauteCommise: 3,
        actionPunition: "Avertissement écrit",
        classe: "1ère A",
        filiere: "Lettres",
        anneeScolaire: "2024-2025"
    },
    {
        id: 2,
        date: "2024-12-14",
        apprenant: "<PERSON><PERSON><PERSON>",
        fauteCommise: 1,
        actionPunition: "Retenue 2h",
        classe: "2ème B",
        filiere: "Sciences",
        anneeScolaire: "2024-2025"
    },
    {
        id: 3,
        date: "2024-12-13",
        apprenant: "Ilunga Patrick",
        fauteCommise: 5,
        actionPunition: "Exclusion temporaire 3 jours",
        classe: "3ème C",
        filiere: "Mathématiques",
        anneeScolaire: "2024-2025"
    },
    {
        id: 4,
        date: "2024-12-12",
        apprenant: "Tshibanda Aline",
        fauteCommise: 2,
        actionPunition: "Travaux d'intérêt général",
        classe: "4ème A",
        filiere: "Lettres",
        anneeScolaire: "2024-2025"
    },
]

</script>
<template>
    <LayoutGestionDisciplinaire active-tag-name="indiscipline">
        <div class="flex flex-col md:flex-row gap-3 md:items-center md:justify-between">
            <div class="relative max-w-sm w-full">
                <Input type="text" id="search" name="search" placeholder="Rechercher une filiere..."
                    class="w-full ps-10 border border-gray-200/40 bg-white transition-all h-9 rounded-md" />
                <div class="absolute left-3 top-1/2 transform -translate-y-1/2 text-foreground-muted/70">
                    <span class="flex iconify hugeicons--search-01 text-sm"></span>
                </div>
            </div>
            <div class="flex flex-wrap items-center gap-2.5">
                <DropdownMenu>
                    <DropdownMenuTrigger as-child>
                        <Button variant="ghost" size="md" class="bg-white border border-border rounded-md ">
                            Exporter
                            <span class="iconify hugeicons--arrow-down-01 " />
                        </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent>
                        <DropdownMenuItem class="flex items-center">
                            <span class="flex mr-1.5 iconify hugeicons--pdf-02"></span>
                            Exporter pdf
                        </DropdownMenuItem>
                        <DropdownMenuItem class="flex items-center">
                            <span class="flex mr-1.5 iconify hugeicons--ai-sheets"></span>
                            Exporter Excel
                        </DropdownMenuItem>
                    </DropdownMenuContent>
                </DropdownMenu>
                <Button size="md" class="rounded-md">
                    <span class="flex iconify hugeicons--plus-sign"></span>
                    Nouveau cas
                </Button>
            </div>
        </div>
        <div class="mt-4 rounded-md overflow-hidden">
            <Table class="rounded-md bg-white">
                <TableHeader>
                    <TableRow>
                        <TableHead class="w-[20px]">
                            <Checkbox class="bg-white scale-70" />
                        </TableHead>
                        <TableHead>Date</TableHead>
                        <TableHead>Apprenant</TableHead>
                        <TableHead>Faute commise</TableHead>
                        <TableHead>Action/Punition</TableHead>
                        <TableHead>Classe</TableHead>
                        <TableHead>Filière</TableHead>
                        <TableHead>Année scolaire</TableHead>
                        <TableHead>

                        </TableHead>
                    </TableRow>
                </TableHeader>
                <TableBody>
                    <TableRow v-for="item in casIndisciplines" :key="item.id">
                        <TableCell class="w-[40px]">
                            <Checkbox class="bg-white scale-70" />
                        </TableCell>
                        <TableCell>{{ item.date }}</TableCell>
                        <TableCell>{{ item.apprenant }}</TableCell>
                        <TableCell>{{ item.fauteCommise }}</TableCell>
                        <TableCell>{{ item.actionPunition }}</TableCell>
                        <TableCell>{{ item.classe }}</TableCell>
                        <TableCell>{{ item.filiere }}</TableCell>
                        <TableCell>{{ item.anneeScolaire }}</TableCell>
                        <TableCell>
                            <div class="flex items-center gap-2 w-max">
                                <Button variant="destructive" size="icon" class="size-8">
                                    <span class="iconify hugeicons--delete-02"></span>
                                </Button>
                            </div>
                        </TableCell>
                    </TableRow>
                </TableBody>
            </Table>
        </div>
    </LayoutGestionDisciplinaire>
</template>