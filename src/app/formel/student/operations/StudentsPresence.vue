<script setup lang="ts">
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from '@/components/ui/table'
import { Checkbox } from '@/components/ui/checkbox';
import { Switch } from '@/components/ui/switch';
import {
    Popover,
    PopoverContent,
    PopoverTrigger,
} from '@/components/ui/popover'
import { SelectContent, Select, SelectTrigger, SelectValue, SelectItem, SelectGroup } from '@/components/ui/select';
import LayoutSaisieOperation from '@/components/templates/LayoutSaisieOperation.vue';
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import BoxPanelWrapper from '@/components/atoms/BoxPanelWrapper.vue';


const studentsPresences = [
    {
        id: 1,
        nom: "Mwamba",
        postnom: "<PERSON><PERSON><PERSON>",
        prenom: "<PERSON>",
        matricule: "ETU001",
        sexe: "M",
        filiere: "Lettres",
        classe: "1ère",
        annee: "2024-2025",
        cycle: "Cycle 1",
        present: true,
        commentaire: "Présent et attentif"
    },
    {
        id: 2,
        nom: "Kasongo",
        postnom: "Mutombo",
        prenom: "Sarah",
        matricule: "ETU002",
        sexe: "F",
        filiere: "Sciences",
        classe: "2ème",
        annee: "2024-2025",
        cycle: "Cycle 1",
        present: false,
        commentaire: "Absent pour raison médicale"
    },
    {
        id: 3,
        nom: "Ilunga",
        postnom: "Kabeya",
        prenom: "Patrick",
        matricule: "ETU003",
        sexe: "M",
        filiere: "Mathématiques",
        classe: "3ème",
        annee: "2024-2025",
        cycle: "Cycle 2",
        present: true,
        commentaire: "Présent"
    },
    {
        id: 4,
        nom: "Tshibanda",
        postnom: "Mbuyi",
        prenom: "Aline",
        matricule: "ETU004",
        sexe: "F",
        filiere: "Lettres",
        classe: "4ème",
        annee: "2024-2025",
        cycle: "Cycle 2",
        present: true,
        commentaire: "Présente et participative"
    },
    {
        id: 5,
        nom: "Kabongo",
        postnom: "Mukendi",
        prenom: "David",
        matricule: "ETU005",
        sexe: "M",
        filiere: "Sciences",
        classe: "1ère",
        annee: "2024-2025",
        cycle: "Cycle 1",
        present: false,
        commentaire: "Retard justifié"
    }
]
</script>

<template>

    <LayoutSaisieOperation active-tag-name="presences" group="operations">
        <BoxPanelWrapper>
            <div class="flex items-center gap-3 justify-between">
                <div class="flex flex-1 items-center gap-2">
                    <div class="relative w-full max-w-xs">
                        <Input type="text" id="search" name="search" placeholder="Rechercher un cours..."
                            class="w-full max-w-xs ps-10 border border-gray-200/40 bg-white transition-all h-10 rounded-md" />
                        <div class="absolute left-3 top-1/2 transform -translate-y-1/2 text-foreground-muted/70">
                            <span class="flex iconify hugeicons--search-01 text-sm"></span>
                        </div>
                    </div>
                    <Popover>
                        <PopoverTrigger as-child>
                            <Button variant="ghost" size="sm" class="h-10 rounded-md border bg-white">
                               <span class="hidden sm:flex"> Filtre</span>
                                <span class="iconify hugeicons--filter">Filtre</span>
                            </Button>
                        </PopoverTrigger>
                        <PopoverContent class="w-80">
                            <div class="grid gap-4">
                                <div class="space-y-2">
                                    <h4 class="font-medium leading-none">
                                        Filtrage
                                    </h4>
                                </div>
                                <div class="flex flex-col gap-3.5">
                                    <div class="space-y-1.5">
                                        <Label class="text-foreground-muted font-light" for="annee">Année
                                            scolaire</Label>
                                        <Select>
                                            <SelectTrigger id="annee" class="!h-10 bg-white w-full">
                                                <SelectValue placeholder="Sélectionnez l'année scolaire" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectGroup>
                                                    <SelectItem value="2024-2025">2024-2025</SelectItem>
                                                    <SelectItem value="2023-2024">2023-2024</SelectItem>
                                                    <SelectItem value="2022-2023">2022-2023</SelectItem>
                                                </SelectGroup>
                                            </SelectContent>
                                        </Select>
                                    </div>
                                    <div class="space-y-1.5">
                                        <Label class="text-foreground-muted font-light" for="filiere">Filière</Label>
                                        <Select>
                                            <SelectTrigger id="filiere" class="!h-10 bg-white w-full">
                                                <SelectValue placeholder="Sélectionnez la filière" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectGroup>
                                                    <SelectItem value="lettres">Lettres</SelectItem>
                                                    <SelectItem value="sciences">Sciences</SelectItem>
                                                    <SelectItem value="mathematiques">Mathématiques</SelectItem>
                                                </SelectGroup>
                                            </SelectContent>
                                        </Select>
                                    </div>
                                    <div class="space-y-1.5">
                                        <Label class="text-foreground-muted font-light" for="classe">Classe</Label>
                                        <Select>
                                            <SelectTrigger id="classe" class="!h-10 bg-white w-full">
                                                <SelectValue placeholder="Sélectionnez la classe" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectGroup>
                                                    <SelectItem value="1ere">1ère</SelectItem>
                                                    <SelectItem value="2eme">2ème</SelectItem>
                                                    <SelectItem value="3eme">3ème</SelectItem>
                                                    <SelectItem value="4eme">4ème</SelectItem>
                                                </SelectGroup>
                                            </SelectContent>
                                        </Select>
                                    </div>
                                    <div class="space-y-1.5">
                                        <Label class="text-foreground-muted font-light" for="date">Date</Label>
                                        <Input id="date" type="date" class="h-10 bg-white" />
                                    </div>
                                    <div class="space-y-1.5">
                                        <Label class="text-foreground-muted font-light" for="cours">Cours</Label>
                                        <Select>
                                            <SelectTrigger id="cours" class="!h-10 bg-white w-full">
                                                <SelectValue placeholder="Sélectionnez le cours" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectGroup>
                                                    <SelectItem value="francais">Français</SelectItem>
                                                    <SelectItem value="mathematiques">Mathématiques</SelectItem>
                                                    <SelectItem value="histoire">Histoire</SelectItem>
                                                    <SelectItem value="geographie">Géographie</SelectItem>
                                                </SelectGroup>
                                            </SelectContent>
                                        </Select>
                                    </div>
                                </div>
                            </div>
                        </PopoverContent>
                    </Popover>
                </div>
                <div class="flex flex-wrap items-center gap-2.5">
                    <DropdownMenu>
                        <DropdownMenuTrigger as-child>
                            <Button variant="ghost" size="md" class="bg-white border border-border rounded-md">
                                Exporter
                                <span class="iconify hugeicons--arrow-down-01 " />
                            </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent>
                            <DropdownMenuItem class="flex items-center">
                                <span class="flex mr-1.5 iconify hugeicons--pdf-02"></span>
                                Exporter pdf
                            </DropdownMenuItem>
                            <DropdownMenuItem class="flex items-center">
                                <span class="flex mr-1.5 iconify hugeicons--ai-sheets"></span>
                                Exporter Excel
                            </DropdownMenuItem>
                        </DropdownMenuContent>
                    </DropdownMenu>
                </div>
            </div>
            <div class="mt-4 rounded-md overflow-hidden">
                <Table class="rounded-md bg-white">
                    <TableHeader>
                        <TableRow>
                            <TableHead class="w-[20px]">
                                <Checkbox class="bg-white scale-70" />
                            </TableHead>
                            <TableHead>
                                Nom complet
                            </TableHead>
                            <TableHead>
                                Classe
                            </TableHead>
                            <TableHead>
                                Présence
                            </TableHead>
                            <TableHead>
                                Commentaire
                            </TableHead>
                        </TableRow>
                    </TableHeader>
                    <TableBody>
                        <TableRow v-for="student in studentsPresences" :key="student.id">
                            <TableCell class="w-[40px]">
                                <Checkbox class="bg-white scale-70" />
                            </TableCell>
                            <TableCell>
                                {{ student.nom }} {{ student.postnom }} {{ student.prenom }}
                            </TableCell>
                            <TableCell>
                                {{ student.classe }} - {{ student.filiere }}
                            </TableCell>
                            <TableCell>
                                <Switch :id="`presence-${student.id}`" :checked="student.present" />
                            </TableCell>
                            <TableCell class="text-left max-w-xs">
                                <Input :value="student.commentaire" placeholder="Ajouter un commentaire..."
                                    class="h-8 text-xs border-gray-200" />
                            </TableCell>
                        </TableRow>
                    </TableBody>
                </Table>
            </div>
        </BoxPanelWrapper>
    </LayoutSaisieOperation>
</template>
