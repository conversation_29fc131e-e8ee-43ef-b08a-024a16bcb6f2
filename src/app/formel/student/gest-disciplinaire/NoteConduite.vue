<script setup lang="ts">
import LayoutGestionDisciplinaire from '@/components/templates/LayoutGestionDisciplinaire.vue';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';


import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from '@/components/ui/table'
import { Checkbox } from '@/components/ui/checkbox';
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'



const notesConduite = [
    {
        id: 1,
        anneeScolaire: "2024-2025",
        filiere: "Lettres",
        classe: "1ère A",
        apprenant: "Mwa<PERSON> Jean",
        numeroFauteCommise: 3,
        conduiteSemestre1: "Bien",
        conduiteSemestre2: "Assez Bien"
    },
    {
        id: 2,
        anneeScolaire: "2024-2025",
        filiere: "Sciences",
        classe: "2ème B",
        apprenant: "Kasongo Sarah",
        numeroFauteCommise: 1,
        conduiteSemestre1: "Très Bien",
        conduiteSemestre2: "Bien"
    },
    {
        id: 3,
        anneeScolaire: "2024-2025",
        filiere: "Mathématiques",
        classe: "3ème C",
        apprenant: "Ilunga Patrick",
        numeroFauteCommise: 5,
        conduiteSemestre1: "Passable",
        conduiteSemestre2: "Médiocre"
    },
    {
        id: 4,
        anneeScolaire: "2024-2025",
        filiere: "Lettres",
        classe: "4ème A",
        apprenant: "Tshibanda Aline",
        numeroFauteCommise: 2,
        conduiteSemestre1: "Bien",
        conduiteSemestre2: "Bien"
    },
]

</script>
<template>
    <LayoutGestionDisciplinaire active-tag-name="note-conduite">
        <div class="flex flex-col md:flex-row gap-3 md:items-center md:justify-between">
            <div class="relative max-w-sm w-full">
                <Input type="text" id="search" name="search" placeholder="Rechercher une filiere..."
                    class="w-full ps-10 border border-gray-200/40 bg-white transition-all h-9 rounded-md" />
                <div class="absolute left-3 top-1/2 transform -translate-y-1/2 text-foreground-muted/70">
                    <span class="flex iconify hugeicons--search-01 text-sm"></span>
                </div>
            </div>
            <div class="flex flex-wrap items-center gap-3">
                <DropdownMenu>
                    <DropdownMenuTrigger as-child>
                        <Button variant="ghost" size="md" class="bg-white border border-border rounded-md ">
                            Exporter
                            <span class="iconify hugeicons--arrow-down-01 " />
                        </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent>
                        <DropdownMenuItem class="flex items-center">
                            <span class="flex mr-1.5 iconify hugeicons--pdf-02"></span>
                            Exporter pdf
                        </DropdownMenuItem>
                        <DropdownMenuItem class="flex items-center">
                            <span class="flex mr-1.5 iconify hugeicons--ai-sheets"></span>
                            Exporter Excel
                        </DropdownMenuItem>
                    </DropdownMenuContent>
                </DropdownMenu>
                <Button size="md" class="">
                    <span class="flex iconify hugeicons--plus-sign"></span>
                    Ajouter
                </Button>
                
            </div>
        </div>
        <div class="mt-4 rounded-md overflow-hidden">
            <Table class="rounded-md bg-white">
                <TableHeader>
                    <TableRow>
                        <TableHead class="w-[20px]">
                            <Checkbox class="bg-white scale-70" />
                        </TableHead>
                        <TableHead>Année scolaire</TableHead>
                        <TableHead>Filière</TableHead>
                        <TableHead>Classe</TableHead>
                        <TableHead>Apprenant</TableHead>
                        <TableHead>N° de faute commise</TableHead>
                        <TableHead>Conduite-semestre 1</TableHead>
                        <TableHead>Conduite-semestre 2</TableHead>
                        <TableHead>

                        </TableHead>
                    </TableRow>
                </TableHeader>
                <TableBody>
                    <TableRow v-for="item in notesConduite" :key="item.id">
                        <TableCell class="w-[40px]">
                            <Checkbox class="bg-white scale-70" />
                        </TableCell>
                        <TableCell>{{ item.anneeScolaire }}</TableCell>
                        <TableCell>{{ item.filiere }}</TableCell>
                        <TableCell>{{ item.classe }}</TableCell>
                        <TableCell>{{ item.apprenant }}</TableCell>
                        <TableCell>{{ item.numeroFauteCommise }}</TableCell>
                        <TableCell>{{ item.conduiteSemestre1 }}</TableCell>
                        <TableCell>{{ item.conduiteSemestre2 }}</TableCell>
                        <TableCell>
                            <div class="flex items-center gap-2 w-max">
                                <Button variant="destructive" size="icon" class="size-8">
                                    <span class="iconify hugeicons--delete-02"></span>
                                </Button>
                            </div>
                        </TableCell>
                    </TableRow>
                </TableBody>
            </Table>
        </div>
    </LayoutGestionDisciplinaire>
</template>