<script setup lang="ts">
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';

import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from '@/components/ui/table'
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { Checkbox } from '@/components/ui/checkbox';

import LayoutSaisieOperation from '@/components/templates/LayoutSaisieOperation.vue';
import NewFormation from '@/components/modals/NewFormation.vue';
import BoxPanelWrapper from '@/components/atoms/BoxPanelWrapper.vue';


const formations = [
    { id: 1, code: "FM1", designation: "Plomberie", duree: 12 },
    { id: 2, code: "FM2", designation: "Electricité", duree: 10 },
    { id: 3, code: "FM3", designation: "<PERSON>uiserie", duree: 8 },
]

</script>

<template>
    <LayoutSaisieOperation current-mode="non-formel" group="saisieNonformel" active-tag-name="formations-organisees">
        <BoxPanelWrapper>
            <div class="flex items-center gap-3 justify-between">
                <div class="relative flex-1">
                    <Input type="text" id="search" name="search" placeholder="Rechercher un cours..."
                        class="w-full max-w-sm ps-10 border border-gray-200/40 bg-white transition-all h-10 rounded-md" />
                    <div class="absolute left-3 top-1/2 transform -translate-y-1/2 text-foreground-muted/70">
                        <span class="flex iconify hugeicons--search-01 text-sm"></span>
                    </div>
                </div>
                <div class="flex flex-wrap items-center gap-2.5">
                    <DropdownMenu>
                        <DropdownMenuTrigger as-child>
                            <Button variant="ghost" size="md" class="bg-white border border-border rounded-md">
                                Exporter
                                <span class="iconify hugeicons--arrow-down-01 " />
                            </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent>
                            <DropdownMenuItem class="flex items-center">
                                <span class="flex mr-1.5 iconify hugeicons--pdf-02"></span>
                                Exporter pdf
                            </DropdownMenuItem>
                            <DropdownMenuItem class="flex items-center">
                                <span class="flex mr-1.5 iconify hugeicons--ai-sheets"></span>
                                Exporter Excel
                            </DropdownMenuItem>
                        </DropdownMenuContent>
                    </DropdownMenu>
                    <NewFormation />
                </div>
            </div>
            <div class="mt-4 rounded-md overflow-hidden">
                <Table class="rounded-md bg-white">
                    <TableHeader>
                        <TableRow>
                            <TableHead class="w-[20px]">
                                <Checkbox class="bg-white scale-70" />
                            </TableHead>
                            <TableHead class="text-left">
                                Code
                            </TableHead>
                            <TableHead>
                                Formation-métier
                            </TableHead>
                            <TableHead>
                                Durée (mois)
                            </TableHead>
                        </TableRow>
                    </TableHeader>
                    <TableBody>
                        <TableRow v-for="item in formations" :key="item.id">
                            <TableCell class="w-[40px]">
                                <Checkbox class="bg-white scale-70" />
                            </TableCell>
                            <TableCell>
                                {{ item.code }}
                            </TableCell>
                            <TableCell>
                                {{ item.designation }}
                            </TableCell>
                            <TableCell>
                                {{ item.duree }}
                            </TableCell>
                        </TableRow>
                    </TableBody>
                </Table>
            </div>
        </BoxPanelWrapper>
    </LayoutSaisieOperation>
</template>
