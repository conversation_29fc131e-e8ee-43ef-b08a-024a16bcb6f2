<script setup lang="ts">
import FilliereItem from '@/components/atoms/FilliereItem.vue';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';

import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'

import LayoutSaisieOperation from '@/components/templates/LayoutSaisieOperation.vue';
import BoxPanelWrapper from '@/components/atoms/BoxPanelWrapper.vue';
import NewFilliere from '@/components/modals/NewFilliere.vue';



const filieres = [
    {
        id: 1,
        text: "Specialisations en arts"
    },
    {
        id: 2,
        text: "Lettres"
    },
    {
        id: 3,
        text: "Langues"
    },
]
</script>

<template>
    <LayoutSaisieOperation active-tag-name="filieres" group="saisie">
        <BoxPanelWrapper>
            <div class="flex items-center gap-3 justify-between">
                <div class="relative flex-1">
                    <Input type="text" id="search" name="search" placeholder="Rechercher une filiere..."
                        class="w-full max-w-sm ps-10 border border-gray-200/40 bg-white transition-all h-10 rounded-md" />
                    <div class="absolute left-3 top-1/2 transform -translate-y-1/2 text-foreground-muted/70">
                        <span class="flex iconify hugeicons--search-01 text-sm"></span>
                    </div>
                </div>
                <div class="flex flex-wrap items-center gap-2.5">
                    <DropdownMenu>
                        <DropdownMenuTrigger as-child>
                            <Button variant="ghost" size="md" class="bg-white border border-border rounded-md">
                                Exporter
                                <span class="iconify hugeicons--arrow-down-01 " />
                            </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent>
                            <DropdownMenuItem class="flex items-center">
                                <span class="flex mr-1.5 iconify hugeicons--pdf-02"></span>
                                Exporter pdf
                            </DropdownMenuItem>
                            <DropdownMenuItem class="flex items-center">
                                <span class="flex mr-1.5 iconify hugeicons--ai-sheets"></span>
                                Exporter Excel
                            </DropdownMenuItem>
                        </DropdownMenuContent>
                    </DropdownMenu>
                    <NewFilliere/>
                </div>
            </div>
            <div class="mt-4">
                <ul class="flex flex-col bg-white rounded-md">
                    <FilliereItem v-for="item in filieres" :key="item.id" :title="item.text" :id="item.id" />
                </ul>
            </div>
        </BoxPanelWrapper>
    </LayoutSaisieOperation>
</template>
