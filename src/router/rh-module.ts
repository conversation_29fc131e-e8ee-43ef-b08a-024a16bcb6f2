import type { RouteRecordRaw } from 'vue-router'

const rhModuleRoutes: RouteRecordRaw[] = [
    //home dahs
    {
        path: "/rh",
        name: "rh-module-home",
        component: () => import('../app/rh/MainDashRh.vue')
    },
    {
        path: "/rh/saisie",
        name: "rh-module-personnel",
        component: () => import('../app/rh/ListePersonnel.vue')
    },
    {
        path: "/rh/saisie/personnel/nouveau",
        name: "rh-module-nouveau-personnel",
        component: () => import('../app/rh/ListePersonnel.vue')
    },

    //saisie prealable
    /**
     * Personnel
     * Presence
     * Mise en place personnel
     * Salaire
     * Evaluation
     * Formation continue
     * Conge
     */
]

export { rhModuleRoutes }