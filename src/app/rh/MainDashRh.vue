<script setup lang="ts">
import PageAnimationWrapper from '@/components/atoms/CAnimationWrapper.vue';
import DashLayout from '@/components/templates/DashLayout.vue';

</script>

<template>
    <DashLayout active-route="/rh" module-name="rh">
        <PageAnimationWrapper>
            <div class="flex flex-col-reverse md:justify-between md:flex-row pt-6 md:pt-12">
                
            </div>
            <div class="pt-7 md:pt-12 pb-20 mx-auto w-full max-w-7xl">
                

            </div>
        </PageAnimationWrapper>
    </DashLayout>
</template>
