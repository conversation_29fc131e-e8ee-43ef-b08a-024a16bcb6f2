<script setup lang="ts">
import type { HTMLAttributes } from 'vue'
import { cn } from '@/lib/utils'

const props = defineProps<{
  class?: HTMLAttributes['class']
}>()
</script>

<template>
  <div
    data-slot="card"
    :class="
      cn(
        'bg-card text-card-foreground flex flex-col gap-6 rounded-3xl border border-border py-6 shadow-lg shadow-gray-200/40',
        props.class,
      )
    "
  >
    <slot />
  </div>
</template>
