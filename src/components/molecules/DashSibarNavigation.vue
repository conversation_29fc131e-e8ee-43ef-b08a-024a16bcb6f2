<script setup lang="ts">
import { modulesNavigation } from "@/data/navigations";
import { cn } from "@/lib/utils";
import {
    Tooltip,
    TooltipContent,
    TooltipTrigger
} from '@/components/ui/tooltip'

const props = defineProps<{
    moduleName: keyof typeof modulesNavigation,
    activeRoute: string
}>()

const items = modulesNavigation[props.moduleName]
</script>
<template>
    <aside
        class="fixed z-50 md:left-0 md:h-screen md:top-0 md:w-24 md:flex md:flex-col md:justify-between md:items-center md:p-4 lg:p-6">
        <div class="hidden md:flex flex-col items-center gap-6">
            <router-link to="/">
                <img src="/pgfe-logo.png" alt="pattern background" class="size-18 object-cover" />
            </router-link>
            <router-link to="/" aria-label="Retour à l'accueil"
                class="rounded-lg bg-white shadow-lg shadow-gray-200/20 size-14 flex items-center justify-center">
                <span class="flex iconify hugeicons--arrow-left-02"></span>
            </router-link>
        </div>
        <div class="flex">
            <div class="menubottom flex flex-1 lg:relative">
                <ul
                    class=" bg-white shadow-lg shadow-gray-200/20 px-2 py-1 [--card-padding:calc(var(--spacing)*2)] [--card-radius:var(--radius-lg)] rounded-(--card-radius) md:py-(--card-padding) md:px-(--card-padding) flex md:flex-col items-center space-x-1 md:space-x-0">
                    <li v-for="item in items.slice(0, 4)" :key="item.id" class="flex-1">
                        <Tooltip>
                            <TooltipTrigger as-child>
                                <RouterLink :to="item.link" :class="cn(
                                    'flex flex-col lg:flex-row justify-center text-center w-full items-center gap-1 px-2 sm:px-3 py-2 md:px-4 md:py-4 rounded-lg lg:raduis-inner text-foreground-muted ease-linear transition-colors', {
                                    'bg-primary text-white': item.link === activeRoute,
                                    'hover:bg-muted': item.link !== activeRoute
                                }
                                )">
                                    <span :class="['iconify text-lg flex', item.icon]" />
                                    <span class="text-xs min-[410px]:text-sm md:hidden lg:text-base line-clamp-1">
                                        {{ item.text }}
                                    </span>
                                </RouterLink>
                            </TooltipTrigger>
                            <TooltipContent>
                                <p> {{ item.text }}</p>
                            </TooltipContent>
                        </Tooltip>
                    </li>
                    <li v-if="items.length >= 5" class="flex-1">
                        <Tooltip>
                            <TooltipTrigger as-child>
                                <button aria-label="Afficher"
                            :class="cn(
                                'flex cursor-pointer flex-col lg:flex-row justify-center text-center w-full items-center gap-1 px-2 sm:px-3 py-2 md:px-4 md:py-4 rounded-lg lg:raduis-inner text-foreground-muted ease-linear transition-colors hover:bg-muted')">
                            <span class="iconify hugeicons--more-02 text-lg flex" />
                            <span class="text-xs min-[410px]:text-sm md:hidden lg:text-base line-clamp-1">
                                Plus
                            </span>
                        </button>
                            </TooltipTrigger>
                            <TooltipContent>
                                <p> Plus d'options</p>
                            </TooltipContent>
                        </Tooltip>
                        
                    </li>
                </ul>
            </div>
        </div>
        <div
            class="hidden md:flex flex-col items-center bg-white shadow-lg shadow-gray-200/20 px-2 py-1 [--card-padding:calc(var(--spacing)*2)] [--card-radius:var(--radius-lg)] rounded-(--card-radius) md:py-(--card-padding) md:px-(--card-padding)">

            <RouterLink to="#" :class="cn(
                'flex flex-col lg:flex-row justify-center text-center w-full items-center gap-1 px-2 sm:px-3 py-2 md:px-4 md:py-4 rounded-lg lg:raduis-inner text-foreground-muted ease-linear transition-colors hover:bg-muted', {}
            )">
                <span :class="['iconify hugeicons--user-02 text-lg flex']" />
            </RouterLink>
            <Tooltip>
                <TooltipTrigger as-child>
                    <RouterLink to="#" :class="cn(
                        'flex flex-col lg:flex-row justify-center text-center w-full items-center gap-1 px-2 sm:px-3 py-2 md:px-4 md:py-4 rounded-lg lg:raduis-inner text-foreground-muted ease-linear transition-colors hover:bg-muted', {}
                    )">
                        <span :class="['iconify hugeicons--setting-07 text-lg flex']" />
                    </RouterLink>
                </TooltipTrigger>
                <TooltipContent>
                    <p>Reglage</p>
                </TooltipContent>
            </Tooltip>
        </div>
    </aside>
</template>
