<script setup lang="ts">
import DashLayout from '@/components/templates/DashLayoutWithMode.vue';
import CAnimationWrapper from '../atoms/CAnimationWrapper.vue';
import { computed } from 'vue';
import { Button } from '../ui/button';
import BoxPanelWrapper from '../atoms/BoxPanelWrapper.vue';


const props = defineProps<{
    group: "saisie" | "operations" | "saisieNonformel" | "operationNonFormel",
    activeTagName: string
    noAnimatedWrapper?: boolean,
    currentMode?: "formel" | "non-formel",
    linkBack: string,
    title: string
}>()



const routeGroups = {
    saisie: "/apprenants/saisie-prealable",
    saisieNonformel: "/apprenants/saisie-prealable",
    operations: "/apprenants/operations",
    operationNonFormel: "/apprenants/operations"
}

const nonFormelLinks = {
    saisie: "/apprenants/formations",
    operations: "/apprenants/inscriptions",
    saisieNonformel: "/apprenants/formations",
    operationNonFormel: "/apprenants/inscriptions"
}

const activeRouteGroup = computed(() => routeGroups[props.group])
const nonFormelLink = computed(() => nonFormelLinks[props.group])
</script>

<template>
    <DashLayout :current-mode="currentMode" :active-route="activeRouteGroup" module-name="students"
        :non-formel-link="nonFormelLink">
        <CAnimationWrapper class="flex flex-col space-y-2 ">
            <div class="flex items-center">
                <Button variant="outline" size="icon" as-child>
                    <RouterLink :to="linkBack" class="flex items-center">
                        <span class="flex iconify hugeicons--arrow-left-02"></span>
                    </RouterLink>
                </Button>
                <h3 class="ml-3 font-semibold text-lg text-foreground-title">
                    {{ title }}
                </h3>
            </div>
            <div class="flex pt-1">
            </div>

            <slot />
        </CAnimationWrapper>
    </DashLayout>
</template>
