import type { RouteRecordRaw } from 'vue-router'

const module1Routes: RouteRecordRaw[] = [
  {
    path: "/apprenants",
    name: "apprenants-home-module",
    component: () => import('../app/formel/student/HomeModule1.vue')
  },
  {
    path: "/apprenants/saisie-prealable",
    name: "apprenants-module-saisie",
    component: () => import('../app/formel/student/saisie-prealable/MainSaisiPrealable.vue')
  },
  {
    path: "/apprenants/saisie-prealable/classes",
    name: "apprenants-module-saisie-classes",
    component: () => import('../app/formel/student/saisie-prealable/SaisiClasse.vue')
  },
  {
    path: "/apprenants/saisie-prealable/cours",
    name: "apprenants-module-saisie-cours",
    component: () => import('../app/formel/student/saisie-prealable/SaisiCours.vue')
  },


  {
    path: "/apprenants/operations",
    name: "apprenants-module-saisie-operation",
    component: () => import('../app/formel/student/operations/MainSaisieOperations.vue')
  },
   {
    path: "/apprenants/operations/nouveau-eleve",
    name: "apprenants-module-saisie-operation-new-inscription",
    component: () => import('../app/formel/student/operations/NewStudent.vue')
  },

  {
    path: "/apprenants/operations/presences",
    name: "apprenants-module-presence",
    component: () => import('../app/formel/student/operations/StudentsPresence.vue')
  },
  {
    path: "/apprenants/operations/fiche-cotation",
    name: "apprenants-module-fiche-cotation",
    component: () => import('../app/formel/student/operations/FicheCotation.vue')
  },
  {
    path: "/apprenants/operations/repechage",
    name: "apprenants-module-fiche-repechage",
    component: () => import('../app/formel/student/operations/ListeRepechage.vue')
  },
  {
    path: "/apprenants/operations/deliberation",
    name: "apprenants-module-fiche-deliberation",
    component: () => import('../app/formel/student/operations/ListeDeliberation.vue')
  },


  {
    path: "/apprenants/operations/validation-laureats",
    name: "apprenants-module-fiche-validation-laureats",
    component: () => import('../app/formel/student/operations/ValidationLaureats.vue')
  },
  {
    path: "/apprenants/operations/visite-classe",
    name: "apprenants-module-fiche-visite-classe",
    component: () => import('../app/formel/student/operations/VisiteClasses.vue')
  },
  {
    path: "/apprenants/operations/gestion-disciplinaire",
    name: "apprenants-module-gestion-disciplinaire",
    component: () => import('../app/formel/student/gest-disciplinaire/SortieElves.vue')
  },
  {
    path: "/apprenants/operations/gestion-disciplinaire/indiscipline",
    name: "apprenants-module-gestion-disciplinaire-indiscipline",
    component: () => import('../app/formel/student/gest-disciplinaire/CasIndisciplines.vue')
  },
  {
    path: "/apprenants/operations/gestion-disciplinaire/abandons",
    name: "apprenants-module-gestion-disciplinaire-abandons",
    component: () => import('../app/formel/student/gest-disciplinaire/ListeAbandon.vue')
  },
  {
    path: "/apprenants/operations/gestion-disciplinaire/note-conduite",
    name: "apprenants-module-gestion-disciplinaire-note-conduite",
    component: () => import('../app/formel/student/gest-disciplinaire/NoteConduite.vue')
  },


  {
    path: "/apprenants/rapports",
    name: "apprenants-module-rapport",
    component: () => import('../app/formel/student/rapports/MainStReport.vue')
  },


  {
    path: "/non-formel/apprenants/formations",
    name: "apprenants-home-module-non-formel",
    component: () => import('../app/non-formel/m-student/saisie-prealable/FormationsOrganisees.vue')
  },
  {
    path: "/non-formel/apprenants/formateurs",
    name: "apprenants-module-non-formel-formateurs",
    component: () => import('../app/non-formel/m-student/saisie-prealable/FormateursResponsables.vue')
  },
    {
    path: "/non-formel/apprenants/formateurs/nouveau",
    name: "apprenants-module-non-formel-formateurs-new",
    component: () => import('../app/non-formel/m-student/saisie-prealable/NewFormatteur.vue')
  },

  {
    path: "/non-formel/apprenants/inscriptions",
    name: "apprenants-home-module-non-formel-inscription",
    component: () => import('../app/non-formel/m-student/operations/MainSaisieOperations.vue')
  },
  {
    path: "/non-formel/apprenants/presences",
    name: "apprenants-module-non-formel-presences",
    component: () => import('../app/non-formel/m-student/operations/StudentsPresence.vue')
  },

  {
    path: "/non-formel/apprenants/fiche-cotation",
    name: "apprenants-module-non-formel-fiche-cotation",
    component: () => import('../app/non-formel/m-student/operations/FicheCotation.vue')
  },
  {
    path: "/non-formel/apprenants/validation-laureats",
    name: "apprenants-module-non-formel-validation-laureats",
    component: () => import('../app/non-formel/m-student/operations/ValidationLaureats.vue')
  },
]

export { module1Routes }