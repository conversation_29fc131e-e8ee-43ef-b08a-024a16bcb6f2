<script setup lang="ts">
import {
    <PERSON><PERSON><PERSON><PERSON><PERSON>, AlertDialog<PERSON>rig<PERSON>, AlertDialogHeader,
    AlertDialogContent,
    AlertDialogTitle, AlertDialogDescription,
    AlertDialogFooter, AlertDialogAction, AlertDialogCancel
} from '../ui/alert-dialog';

import { Button } from "./../ui/button"



const props = defineProps<{
    title: string,
    message: string,
    action?: "warning" | "success"|"danger" | "default"
}>()

const actions = {
    warning: {
        icon: "hugeicons--warning",
        class: "bg-orange-100/50 dark:bg-orange-900/50 text-orange-600"
    },
    danger: {
        icon: "hugeicons--delete-02",
        class: "bg-red-100/50 dark:bg-red-900/50 text-red-600"
    },
    success: {
        icon: "hugeicons--success-01",
        class: "bg-emerald-100/50 dark:bg-emerald-900/50 text-emerald-600"
    },
    default: {
        icon: "",
        class: "bg-primary-100/50 dark:bg-primary-900/50 text-primary-600"
    }
}

const selectedAction = actions[props.action ?? "default"]
</script>
<template>
    <AlertDialog>
        <AlertDialogTrigger as-child>
            <slot name="trigger" />
        </AlertDialogTrigger>
        <AlertDialogContent class="flex flex-col items-center text-center hugeicons--tr">
            <div :class="['mx-auto size-12 flex items-center justify-center rounded-full', selectedAction.class]">
                <span :class="['flex iconify text-xl', selectedAction.icon]" />
            </div>
            <AlertDialogHeader>
                <AlertDialogTitle class="text-center">
                    {{ title }}
                </AlertDialogTitle>
                <AlertDialogDescription class="text-center">
                    {{ message }}
                </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter class="flex justify-center mt-4 items-center">
                <AlertDialogCancel as-child>
                    <Button type="reset" size="sm" variant="outline" class="h-10">
                        Non, Annuler
                    </Button>
                </AlertDialogCancel>
                <AlertDialogAction no-class as-child>
                    <slot name="confirm-action-button" />
                </AlertDialogAction>
            </AlertDialogFooter>
        </AlertDialogContent>
    </AlertDialog>
</template>