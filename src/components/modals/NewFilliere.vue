<script setup lang="ts">
import { Button } from '@/components/ui/button'
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'

</script>

<template>
    <Dialog>
        <DialogTrigger as-child>
            <Button size="md" class="rounded-md">
                <span class="iconify hugeicons--plus-sign"></span>
                <span class="hidden sm:flex">
                    Nouvelle fillière
                </span>
            </Button>
        </DialogTrigger>
        <DialogContent class="sm:max-w-[400px]">
            <DialogHeader>
                <DialogTitle>Nouvelle fillière</DialogTitle>
                <DialogDescription>
                    Enregistrer une nouvelle fillière
                </DialogDescription>
            </DialogHeader>
            <div class="grid gap-4 py-4">
                <div class="flex flex-col space-y-1.5">
                    <Label for="intitule_cours" class="text-sm font-medium">
                        Intitulé du cours
                    </Label>
                    <Input type="text" id="intitule_cours" name="intitule_cours"
                        placeholder="Entrez l'intitulé du cours"
                        class="w-full h-10 border border-gray-200/40 bg-white transition-all" />
                </div>
            </div>
            <DialogFooter class="flex justify-end gap-2 items-center">
                <Button size="sm" class="h-9" variant="outline">
                    <span class="iconify flex hugeicons--cancel-01 mr-1"></span>
                    Annuler
                </Button>
                <Button size="sm" class="h-9" type="submit">
                    <span class="iconify flex hugeicons--floppy-disk mr-1"></span>
                    Enregistrer
                </Button>
            </DialogFooter>
        </DialogContent>
    </Dialog>
</template>