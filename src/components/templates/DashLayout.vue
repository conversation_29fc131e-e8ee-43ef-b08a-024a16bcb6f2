<script setup lang="ts">
import { modulesNavigation } from '@/data/navigations';
import DashSibarNavigation from '../molecules/DashSibarNavigation.vue';
import {
    TooltipProvider
} from '@/components/ui/tooltip'
import GlobalDashHeader from '../molecules/GlobalDashHeader.vue';
import AppBreadcrumb from '../molecules/AppBreadcrumb.vue';
import type { BreadcrumbProps } from '@/types';


defineProps({
    moduleName: {
        type: String as () => keyof typeof modulesNavigation,
        required: true
    },
    activeRoute: {
        type: String,
        required: true
    },
    breadcrumb: {
        type: Array as () => BreadcrumbProps[],
        required: true
    }
});
</script>
<template>
    <TooltipProvider>
        <DashSibarNavigation :activeRoute="activeRoute" :module-name="moduleName" />
        <div class="w-full h-max md:pl-24 pb-8">
            <GlobalDashHeader />
            <div
                class="flex flex-col-reverse md:justify-between md:items-center md:flex-row pt-8 lg:pt-2 mx-auto max-w-7xl px-4 sm:px-8 md:px-10 lg:px-12">
                <div class="flex flex-col max-w-2xl">
                    <AppBreadcrumb :items="[
                        { label: 'Menu', href: '/', icon: 'hugeicons--home-01' },
                        { label: 'GRH', href: '/rh' },
                        { label: 'Personnel', isActive: true }
                    ]" />
                </div>
                <div class="relative h-max max-md:flex-1">
                    <Input type="text" id="search" name="search" placeholder="Rechercher une classe..."
                        class="w-full max-w-sm pe-10 border border-gray-200 bg-white shadow-lg shadow-gray-200/20 transition-all h-10 px-3 rounded-md" />
                    <div
                        class="absolute bg-primary p-2 rounded-md right-1 top-1/2 transform -translate-y-1/2 text-white">
                        <span class="flex iconify hugeicons--search-01 text-xs"></span>
                    </div>
                </div>
            </div>
            <div aria-hidden="true" class="h-16 md:h-8 flex"></div>
            <slot />
        </div>
    </TooltipProvider>
</template>
