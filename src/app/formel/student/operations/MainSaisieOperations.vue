<script setup lang="ts">
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from '@/components/ui/table'
import { Checkbox } from '@/components/ui/checkbox';
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'

import LayoutSaisieOperation from '@/components/templates/LayoutSaisieOperation.vue';
import BoxPanelWrapper from '@/components/atoms/BoxPanelWrapper.vue';



const students = [
    {
        id: 1,
        nom: "Mwamba",
        postnom: "Kabasele",
        prenom: "Jean",
        sexe: "M",
        filiere: "Lettres",
        classe: "1ère",
        annee: "2024-2025",
        cycle: "Cycle 1"
    },
    {
        id: 2,
        nom: "Kason<PERSON>",
        postnom: "<PERSON><PERSON><PERSON>",
        prenom: "<PERSON>",
        sexe: "F",
        filiere: "Sciences",
        classe: "2ème",
        annee: "2024-2025",
        cycle: "Cycle 1"
    },
    {
        id: 3,
        nom: "Ilunga",
        postnom: "Kabeya",
        prenom: "Patrick",
        sexe: "M",
        filiere: "Mathématiques",
        classe: "3ème",
        annee: "2024-2025",
        cycle: "Cycle 2"
    },
    {
        id: 4,
        nom: "Tshibanda",
        postnom: "Mbuyi",
        prenom: "Aline",
        sexe: "F",
        filiere: "Lettres",
        classe: "4ème",
        annee: "2024-2025",
        cycle: "Cycle 2"
    },
]
</script>

<template>
    <LayoutSaisieOperation active-tag-name="inscriptions" group="operations">
        <BoxPanelWrapper>
            <div class="flex sm:items-center gap-3 flex-col sm:flex-row sm:justify-between">
                <div class="relative flex-1">
                    <Input type="text" id="search" name="search" placeholder="Rechercher un cours..."
                        class="w-full max-w-sm ps-10 border border-gray-200/40 bg-white transition-all h-10 rounded-md" />
                    <div class="absolute left-3 top-1/2 transform -translate-y-1/2 text-foreground-muted/70">
                        <span class="flex iconify hugeicons--search-01 text-sm"></span>
                    </div>
                </div>
                <div class="flex flex-wrap items-center sm:justify-end gap-2.5 flex-1">
                    <Button size="md" class="rounded-md max-sm:flex-1 sm:w-max" as-child>
                       <RouterLink to="/apprenants/operations/nouveau-eleve">
                         <span class="flex iconify hugeicons--plus-sign"></span>
                        <span class="hidden sm:flex">Nouveau Eleve</span>
                       </RouterLink>
                    </Button>
                    <DropdownMenu>
                        <DropdownMenuTrigger as-child>
                            <Button variant="ghost" size="md" class="bg-white border border-border rounded-md">
                                Exporter
                                <span class="iconify hugeicons--arrow-down-01 " />
                            </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent>
                            <DropdownMenuItem class="flex items-center">
                                <span class="flex mr-1.5 iconify hugeicons--pdf-02"></span>
                                Exporter pdf
                            </DropdownMenuItem>
                            <DropdownMenuItem class="flex items-center">
                                <span class="flex mr-1.5 iconify hugeicons--ai-sheets"></span>
                                Exporter Excel
                            </DropdownMenuItem>
                        </DropdownMenuContent>
                    </DropdownMenu>
                </div>
            </div>
            <div class="mt-4 rounded-md overflow-hidden">
                <Table class="rounded-md bg-white">
                    <TableHeader>
                        <TableRow>
                            <TableHead class="w-[20px]">
                                <Checkbox class="bg-white scale-70" />
                            </TableHead>
                            <TableHead>Nom</TableHead>
                            <TableHead>Postnom</TableHead>
                            <TableHead>Prénom</TableHead>
                            <TableHead>Sexe</TableHead>
                            <TableHead>Filière</TableHead>
                            <TableHead>Classe</TableHead>
                            <TableHead>Année scolaire</TableHead>
                            <TableHead>Cycle</TableHead>
                            <TableHead>
                                Operations
                            </TableHead>
                        </TableRow>
                    </TableHeader>
                    <TableBody>
                        <TableRow v-for="item in students" :key="item.id">
                            <TableCell class="w-[40px]">
                                <Checkbox class="bg-white scale-70" />
                            </TableCell>
                            <TableCell>{{ item.nom }}</TableCell>
                            <TableCell>{{ item.postnom }}</TableCell>
                            <TableCell>{{ item.prenom }}</TableCell>
                            <TableCell>{{ item.sexe }}</TableCell>
                            <TableCell>{{ item.filiere }}</TableCell>
                            <TableCell>{{ item.classe }}</TableCell>
                            <TableCell>{{ item.annee }}</TableCell>
                            <TableCell>{{ item.cycle }}</TableCell>
                            <TableCell>
                                <div class="flex items-center gap-2 w-max">
                                    <Button variant="outline" size="icon" class="size-8">
                                        <span class="iconify hugeicons--edit-02"></span>
                                    </Button>
                                    <Button variant="destructive" size="icon" class="size-8">
                                        <span class="iconify hugeicons--delete-02"></span>
                                    </Button>
                                </div>
                            </TableCell>
                        </TableRow>
                    </TableBody>
                </Table>
            </div>
        </BoxPanelWrapper>
    </LayoutSaisieOperation>
</template>
