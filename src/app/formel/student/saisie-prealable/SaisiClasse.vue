<script setup lang="ts">
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from '@/components/ui/table'
import { Checkbox } from '@/components/ui/checkbox';
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import LayoutSaisieOperation from '@/components/templates/LayoutSaisieOperation.vue';
import BoxPanelWrapper from '@/components/atoms/BoxPanelWrapper.vue';
import NewClasse from '@/components/modals/NewClasse.vue';



const classes = [
    {
        id: 1,
        code: "C1",
        designation: "Classe 1",
        niveau: "1",
        filiere: "Lettres",
        cycle: "Cycle 1"
    },
    {
        id: 2,
        code: "C2",
        designation: "Classe 2",
        niveau: "2",
        filiere: "Lettres",
        cycle: "Cycle 1"
    },
    {
        id: 3,
        code: "C3",
        designation: "Classe 3",
        niveau: "3",
        filiere: "Lettres",
        cycle: "Cycle 1"
    },
]
</script>

<template>
    <LayoutSaisieOperation active-tag-name="classes" group="saisie">
        <BoxPanelWrapper>
            <div class="flex items-center gap-3 justify-between">
                <div class="relative flex-1">
                    <Input type="text" id="search" name="search" placeholder="Rechercher une classe..."
                        class="w-full max-w-sm ps-10 border border-gray-200/40 bg-white transition-all h-10 rounded-md" />
                    <div class="absolute left-3 top-1/2 transform -translate-y-1/2 text-foreground-muted/70">
                        <span class="flex iconify hugeicons--search-01 text-sm"></span>
                    </div>
                </div>
                <div class="flex flex-wrap items-center gap-2.5">
                    <DropdownMenu>
                        <DropdownMenuTrigger as-child>
                            <Button variant="ghost" size="md" class="bg-white border border-border rounded-md">
                                Exporter
                                <span class="iconify hugeicons--arrow-down-01 " />
                            </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent>
                            <DropdownMenuItem class="flex items-center">
                                <span class="flex mr-1.5 iconify hugeicons--pdf-02"></span>
                                Exporter pdf
                            </DropdownMenuItem>
                            <DropdownMenuItem class="flex items-center">
                                <span class="flex mr-1.5 iconify hugeicons--ai-sheets"></span>
                                Exporter Excel
                            </DropdownMenuItem>
                        </DropdownMenuContent>
                    </DropdownMenu>
                    <NewClasse />
                </div>
            </div>
            <div class="mt-4 rounded-md overflow-hidden flex flex-1 bg-white">
                <Table class="rounded-md">
                    <TableHeader>
                        <TableRow>
                            <TableHead class="w-[20px]">
                                <Checkbox class="bg-white scale-70" />
                            </TableHead>
                            <TableHead class="text-left">
                                Code
                            </TableHead>
                            <TableHead>
                                Designation
                            </TableHead>
                            <TableHead>
                                Niveau
                            </TableHead>
                            <TableHead>
                                Filière
                            </TableHead>
                            <TableHead>
                                Cycle
                            </TableHead>
                        </TableRow>
                    </TableHeader>
                    <TableBody>
                        <TableRow v-for="item in classes" :key="item.id">
                            <TableCell class="w-[40px]">
                                <Checkbox class="bg-white scale-70" />
                            </TableCell>
                            <TableCell>
                                {{ item.code }}
                            </TableCell>
                            <TableCell>
                                {{ item.designation }}
                            </TableCell>
                            <TableCell class="text-left">
                                {{ item.niveau }}
                            </TableCell>
                            <TableCell>
                                {{ item.filiere }}
                            </TableCell>
                            <TableCell>
                                {{ item.cycle }}
                            </TableCell>
                        </TableRow>
                    </TableBody>
                </Table>
            </div>
            <div class="border-t border-border pt-3 mt-3 flex justify-between w-full">
                <nav aria-label="Page navigation example">
                    <ul class="inline-flex -space-x-px text-sm">
                        <li>
                            <a href="#"
                                class="flex items-center justify-center px-3 h-8 ms-0 leading-tight text-fg-muted bg-white border border-e-0 border-border rounded-s-lg hover:bg-gray-100 hover:text-gray-700">
                                Précédent
                            </a>
                        </li>
                        <li>
                            <a href="#"
                                class="flex items-center justify-center px-3 h-8 leading-tight text-fg-muted bg-white border border-border hover:bg-gray-100 hover:text-gray-700">1</a>
                        </li>
                        <li>
                            <a href="#" aria-current="page"
                                class="flex items-center justify-center px-3 h-8 text-primary-600 border border-border bg-primary-50 hover:bg-primary-100 hover:text-primary-700">2</a>
                        </li>
                        <li>
                            <a href="#"
                                class="flex items-center justify-center px-3 h-8 leading-tight text-fg-muted bg-white border border-border rounded-e-lg hover:bg-gray-100 hover:text-gray-700">
                                Suivant
                            </a>
                        </li>
                    </ul>
                </nav>
            </div>
        </BoxPanelWrapper>
    </LayoutSaisieOperation>
</template>
