<script setup lang="ts">
import PageAnimationWrapper from '@/components/atoms/CAnimationWrapper.vue';
import CardDashStat from '@/components/molecules/CardDashStat.vue';
import DashLayout from '@/components/templates/DashLayoutWithMode.vue';
import { Bar<PERSON>hart } from '@/components/ui/chart-bar'
import { Select, SelectItem, SelectTrigger, SelectGroup, SelectValue, SelectContent } from '@/components/ui/select';

const data = [
    { name: 'Jan<PERSON>', fille: 1200, garcon: 4500 },
    { name: '<PERSON>év<PERSON>', fille: 1100, garcon: 1400 },
    { name: 'Mars', fille: 1300, garcon: 1600 },
    { name: 'Avril', fille: 1250, garcon: 1550 },
    { name: 'Mai', fille: 1400, garcon: 1700 },
    { name: 'Juin', fille: 3350, garcon: 3650 },
    { name: '<PERSON><PERSON><PERSON>', fille: 1500, garcon: 1800 },
    { name: '<PERSON><PERSON><PERSON><PERSON>', fille: 1450, garcon: 1750 },
    { name: 'Septembre', fille: 1600, garcon: 4900 },
    { name: 'Octobre', fille: 1550, garcon: 1850 },
    { name: 'Novem<PERSON>', fille: 1700, garcon: 2000 },
    { name: 'Décembre', fille: 1650, garcon: 1950 },
]


</script>

<template>
    <DashLayout non-formel-link="/apprenants" :show-switcher="false" active-route="/apprenants" module-name="students">
        <PageAnimationWrapper>
            <div class="flex flex-col-reverse md:justify-between md:flex-row pt-6 md:pt-12">
                <div class="flex flex-col max-w-2xl">
                    <h1 class="font-semibold text-xl text-foreground-title">Dashboard</h1>
                    <p class="text-foreground-muted mt-0.5">
                        Bonjour In Afrique, Bienvenu à nouveau sur votre plateforme de gestion.
                    </p>
                </div>
                <div class="relative h-max max-md:flex-1">
                    <Input type="text" id="search" name="search" placeholder="Rechercher une classe..."
                        class="w-full max-w-sm pe-10 border border-gray-200/40 bg-white transition-all h-10 px-3 rounded-md" />
                    <div
                        class="absolute bg-primary p-1.5 rounded-md right-2 top-1/2 transform -translate-y-1/2 text-white">
                        <span class="flex iconify hugeicons--search-01 text-xs"></span>
                    </div>
                </div>
            </div>
            <div class="pt-7 md:pt-12 pb-20 mx-auto w-full max-w-7xl">
                <div class="grid sm:grid-cols-2 lg:grid-cols-4 gap-6">
                    <CardDashStat icon="hugeicons--user" :value="10000" title="Apprenants"
                        description="Total des apprenants" color="#008080" />
                    <CardDashStat icon="hugeicons--user" :value="10000" title="Apprenants"
                        description="Total des apprenants" color="#008080" />
                    <CardDashStat icon="hugeicons--user" :value="10000" title="Apprenants"
                        description="Total des apprenants" color="#008080" />
                    <CardDashStat icon="hugeicons--user" :value="10000" title="Apprenants"
                        description="Total des apprenants" color="#008080" />
                </div>
                <div class="">

                </div>
                <BarChart :data="data" index="name" :categories="['fille', 'garcon']" :rounded-corners="30"
                    class="mt-10 bg-white rounded-lg p-3">
                    <template #title>
                        <h3 class="text-foreground-title font-semibold text-lg">
                            Apprenants
                        </h3>
                    </template>
                    <template #actions>
                        <div>
                            <Select>
                                <SelectTrigger class="!h-9 rounded-md border bg-white">
                                    <SelectValue placeholder="Formels" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectGroup>
                                        <SelectItem value="formel">Formel</SelectItem>
                                        <SelectItem value="informel">Informel</SelectItem>
                                    </SelectGroup>
                                </SelectContent>
                            </Select>
                        </div>
                    </template>
                </BarChart>

            </div>
        </PageAnimationWrapper>
    </DashLayout>
</template>
