<script setup lang="ts">
import DashPageHeader from '@/components/templates/DashPageHeader.vue';
import { tagRhNavSaisie } from './rh-tags-links';

import { computed } from 'vue';
import DashLayout from '../DashLayout.vue';
import CAnimationWrapper from '@/components/atoms/CAnimationWrapper.vue';


const props = defineProps<{
    group: "saisie",
    activeTagName: string
    noAnimatedWrapper?: boolean,
}>()

const groups = {
    saisie: tagRhNavSaisie,
}

const routeGroups = {
    saisie: "/rh/saisie",
    operations: "/rh/operations",
}


const activeGroupTags = computed(() => groups[props.group])
const activeRouteGroup = computed(() => routeGroups[props.group])

</script>

<template>
    <DashLayout :active-route="activeRouteGroup" module-name="rh">
        <CAnimationWrapper no-animated>
            <div class="pb-6 mx-auto w-full max-w-7xl">
                <DashPageHeader title="Enseignement formel" :tags="activeGroupTags" :active-tag-name="activeTagName" />
                <CAnimationWrapper :no-animated="noAnimatedWrapper" as="div">
                    <slot />
                </CAnimationWrapper>
            </div>
        </CAnimationWrapper>
    </DashLayout>
</template>
