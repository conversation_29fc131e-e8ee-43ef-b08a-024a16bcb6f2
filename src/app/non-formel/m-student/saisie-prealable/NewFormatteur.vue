<script setup lang="ts">
import FormSection from '@/components/atoms/FormSection.vue';
import InputWrapper from '@/components/atoms/InputWrapper.vue';
import DashFormLayout from '@/components/templates/DashFormLayout.vue';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';


</script>
<template>
    <DashFormLayout title="Enregistrer un nouveau formateur" link-back="/non-formel/apprenants/formateurs"
        current-mode="non-formel" group="saisieNonformel" active-tag-name="formateur-responsable">
        <form class="w-full flex flex-col space-y-8">
            <FormSection class="grid sm:grid-cols-2 gap-y-6 gap-x-10" title="Informations personnelles">
                <div class="flex flex-col gap-6 w-full">
                    <InputWrapper>
                        <Label class="text-sm">
                            Nom
                        </Label>
                        <Input class="bg-white transition-all h-10 rounded-md" placeholder="Kasongo" />
                    </InputWrapper>
                    <InputWrapper>
                        <Label class="text-sm">
                            Post nom
                        </Label>
                        <Input class="bg-white transition-all h-10 rounded-md" placeholder="Muleka" />
                    </InputWrapper>
                    <InputWrapper>
                        <Label class="text-sm">
                            Prenom
                        </Label>
                        <Input class="bg-white transition-all h-10 rounded-md" placeholder="Isaac" />
                    </InputWrapper>
                </div>
                <div class="flex flex-col gap-6 w-full">
                    <InputWrapper>
                        <Label class="text-sm">
                            Nom
                        </Label>
                        <Input class="bg-white transition-all h-10 rounded-md" placeholder="Kasongo" />
                    </InputWrapper>
                    <InputWrapper>
                        <Label class="text-sm">
                            Post nom
                        </Label>
                        <Input class="bg-white transition-all h-10 rounded-md" placeholder="Muleka" />
                    </InputWrapper>
                    <InputWrapper>
                        <Label class="text-sm">
                            Prenom
                        </Label>
                        <Input class="bg-white transition-all h-10 rounded-md" placeholder="Isaac" />
                    </InputWrapper>
                </div>
            </FormSection>
            <div class="w-full flex h-px bg-gray-300"></div>
            <FormSection class="grid sm:grid-cols-2 gap-y-6 gap-x-10" title="Informations personnelles">
                <div class="flex flex-col gap-6 w-full">
                    <InputWrapper>
                        <Label class="text-sm">
                            Nom
                        </Label>
                        <Input class="bg-white transition-all h-10 rounded-md" placeholder="Kasongo" />
                    </InputWrapper>
                    <InputWrapper>
                        <Label class="text-sm">
                            Post nom
                        </Label>
                        <Input class="bg-white transition-all h-10 rounded-md" placeholder="Muleka" />
                    </InputWrapper>
                    <InputWrapper>
                        <Label class="text-sm">
                            Prenom
                        </Label>
                        <Input class="bg-white transition-all h-10 rounded-md" placeholder="Isaac" />
                    </InputWrapper>
                </div>
                <div class="flex flex-col gap-6 w-full">
                    <InputWrapper>
                        <Label class="text-sm">
                            Nom
                        </Label>
                        <Input class="bg-white transition-all h-10 rounded-md" placeholder="Kasongo" />
                    </InputWrapper>
                    <InputWrapper>
                        <Label class="text-sm">
                            Post nom
                        </Label>
                        <Input class="bg-white transition-all h-10 rounded-md" placeholder="Muleka" />
                    </InputWrapper>
                    <InputWrapper>
                        <Label class="text-sm">
                            Prenom
                        </Label>
                        <Input class="bg-white transition-all h-10 rounded-md" placeholder="Isaac" />
                    </InputWrapper>
                </div>
            </FormSection>
            <div class="flex items-center justify-end gap-2">
                <Button variant="outline">
                    <span class="flex iconify hugeicons--cancel-01 mr-1.5"></span>
                    Annuler
                </Button>
                <Button>
                    <span class="flex iconify hugeicons--floppy-disk mr-1.5"></span>
                    Enregistrer
                </Button>
            </div>
        </form>
    </DashFormLayout>
</template>